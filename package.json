{"name": "storybook", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build", "chromatic": "npx chromatic --project-token=chpt_7767dd47fd59d0a --auto-accept-changes"}, "dependencies": {"react": "^19.1.0", "react-dom": "^19.1.0"}, "devDependencies": {"@chromatic-com/storybook": "^4.0.1", "@eslint/js": "^9.30.1", "@storybook/addon-a11y": "^9.1.0", "@storybook/addon-docs": "^9.1.0", "@storybook/addon-vitest": "^9.1.0", "@storybook/react-vite": "^9.1.0", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.6.0", "@vitest/browser": "^3.2.4", "@vitest/coverage-v8": "^3.2.4", "chromatic": "^13.1.3", "@react-pdf/renderer": "^4.3.0", "@ant-design/icons": "^4.8.0", "@types/crypto-js": "^4.1.2", "eslint": "^9.30.1", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "eslint-plugin-storybook": "^9.1.0", "globals": "^16.3.0", "playwright": "^1.54.1", "storybook": "^9.1.0", "typescript": "~5.8.3", "typescript-eslint": "^8.35.1", "vite": "^7.0.4", "vitest": "^3.2.4", "antd": "^5.2.2"}}